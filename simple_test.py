#!/usr/bin/env python3
"""
简单的CMS Commander插件测试
检查插件是否响应我们的请求
"""

import requests
import json

def test_cms_commander(target_url):
    """测试CMS Commander插件响应"""
    
    print(f"测试目标: {target_url}")
    
    # 创建会话
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'CMS Commander Client'
    })
    
    # 测试1: 发送简单的测试请求
    print("\n=== 测试1: 简单测试请求 ===")
    
    test_payload = {
        "cmsc": "yes",
        "action": "test",
        "params": {"test": "hello"},
        "id": "12345",
        "signature": "test_signature",
        "url": target_url
    }
    
    data = f"##CMSC##{json.dumps(test_payload)}"
    
    try:
        response = session.post(
            target_url,
            data=data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        
        # 检查是否包含CMS Commander相关的响应
        if "CMSCHEADER" in response.text:
            print("[+] 发现CMS Commander响应标识！")
        elif "cmsc" in response.text.lower():
            print("[+] 响应中包含CMSC相关内容")
        elif response.status_code == 200 and len(response.text) < 1000:
            print("[?] 可能的插件响应（短响应）")
            print(f"响应内容: {response.text[:200]}")
        else:
            print("[-] 标准WordPress页面响应")
            
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试2: 尝试获取插件信息
    print("\n=== 测试2: 获取插件信息 ===")
    
    info_payload = {
        "cmsc": "yes", 
        "action": "get_settings",
        "params": {"username": "admin"},
        "id": "12346",
        "signature": "test_signature",
        "url": target_url
    }
    
    data = f"##CMSC##{json.dumps(info_payload)}"
    
    try:
        response = session.post(
            target_url,
            data=data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        # 查找错误消息或响应
        if "error" in response.text.lower():
            print("[+] 发现错误响应，插件可能在处理请求")
        elif "signature" in response.text.lower():
            print("[+] 发现签名相关错误，确认插件在处理请求")
        elif "authentication" in response.text.lower():
            print("[+] 发现认证相关错误，确认插件在处理请求")
            
        # 显示响应的关键部分
        lines = response.text.split('\n')
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in ['cmsc', 'error', 'signature', 'authentication']):
                print(f"第{i+1}行: {line.strip()}")
                
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试3: 检查原始POST数据处理
    print("\n=== 测试3: 原始POST数据 ===")
    
    # 直接发送原始数据，不使用表单编码
    raw_data = f"##CMSC##{json.dumps(test_payload)}"
    
    try:
        response = session.post(
            target_url,
            data=raw_data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': str(len(raw_data))
            },
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        # 检查是否有不同的响应
        if response.text != session.get(target_url).text:
            print("[+] POST响应与GET响应不同，可能被处理")
            
            # 查找特殊标识
            special_markers = ['CMSCHEADER', 'cmsc_response', 'Invalid', 'signature']
            for marker in special_markers:
                if marker in response.text:
                    print(f"[+] 发现标识: {marker}")
                    
    except Exception as e:
        print(f"请求失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    target = "http://192.168.76.135/"
    test_cms_commander(target)
