#!/usr/bin/env python3
"""
直接测试CMS Commander插件
基于代码分析的精确测试
"""

import requests
import json
import sys

def test_plugin_response(target_url):
    """测试插件响应"""
    
    print(f"目标: {target_url}")
    
    # 根据插件代码，构造正确的请求
    payload = {
        "cmsc": "yes",
        "action": "get_settings", 
        "params": {
            "username": "admin"
        },
        "id": "123456",
        "signature": "dGVzdF9zaWduYXR1cmU=",  # base64编码的测试签名
        "url": target_url.rstrip('/')
    }
    
    # 按照插件期望的格式构造数据
    post_data = "##CMSC##" + json.dumps(payload)
    
    print(f"发送数据: {post_data}")
    print()
    
    try:
        # 发送请求
        response = requests.post(
            target_url,
            data=post_data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'CMS Commander Client/2.288'
            },
            timeout=10
        )
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)} 字节")
        print()
        
        # 分析响应
        response_text = response.text
        
        # 检查是否是标准WordPress页面
        if '<!DOCTYPE html>' in response_text and len(response_text) > 10000:
            print("[-] 收到标准WordPress页面，插件可能未处理请求")
            
            # 但仍然检查是否有插件相关内容
            if 'cmsc' in response_text.lower():
                print("[?] 页面中包含CMSC相关内容")
            
        else:
            print("[+] 收到非标准响应，可能是插件响应")
            print("响应内容:")
            print("-" * 50)
            print(response_text[:1000])  # 显示前1000个字符
            print("-" * 50)
        
        # 检查特定的错误消息
        error_indicators = [
            "Invalid message",
            "signature", 
            "authentication",
            "CMSCHEADER",
            "cmsc_response",
            "worker_message_id"
        ]
        
        found_indicators = []
        for indicator in error_indicators:
            if indicator in response_text:
                found_indicators.append(indicator)
        
        if found_indicators:
            print(f"[+] 发现插件相关标识: {', '.join(found_indicators)}")
            print("[+] 确认插件正在处理请求！")
        else:
            print("[-] 未发现插件处理标识")
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return False
    
    return True

def test_different_actions(target_url):
    """测试不同的动作"""
    
    actions_to_test = [
        "get_settings",
        "save_settings", 
        "test",
        "ping",
        "get_worker_message_id"
    ]
    
    print("\n=== 测试不同动作 ===")
    
    for action in actions_to_test:
        print(f"\n测试动作: {action}")
        
        payload = {
            "cmsc": "yes",
            "action": action,
            "params": {"username": "admin"},
            "id": "123456",
            "signature": "dGVzdF9zaWduYXR1cmU=",
            "url": target_url.rstrip('/')
        }
        
        post_data = "##CMSC##" + json.dumps(payload)
        
        try:
            response = requests.post(
                target_url,
                data=post_data,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'CMS Commander Client/2.288'
                },
                timeout=5
            )
            
            # 检查响应是否不同于标准页面
            if response.status_code != 200:
                print(f"  状态码: {response.status_code}")
            elif len(response.text) < 5000:  # 短响应可能是插件响应
                print(f"  短响应 ({len(response.text)} 字节): {response.text[:200]}")
            elif any(keyword in response.text for keyword in ['error', 'invalid', 'signature']):
                print(f"  包含错误信息的响应")
            else:
                print(f"  标准页面响应")
                
        except Exception as e:
            print(f"  请求失败: {e}")

if __name__ == "__main__":
    target = "http://192.168.76.135/"
    
    print("CMS Commander 直接测试工具")
    print("=" * 40)
    
    if test_plugin_response(target):
        test_different_actions(target)
    
    print("\n测试完成")
