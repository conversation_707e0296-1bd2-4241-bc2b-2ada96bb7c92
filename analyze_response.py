#!/usr/bin/env python3
"""
分析CMS Commander插件的响应
查看具体的错误信息和响应内容
"""

import requests
import json
import re

def analyze_plugin_response(target_url):
    """分析插件响应"""
    
    print(f"分析目标: {target_url}")
    
    # 测试载荷
    payload = {
        "cmsc": "yes",
        "action": "get_settings",
        "params": {"username": "admin"},
        "id": "123456", 
        "signature": "dGVzdF9zaWduYXR1cmU=",
        "url": target_url.rstrip('/')
    }
    
    post_data = "##CMSC##" + json.dumps(payload)
    
    try:
        # 发送请求
        response = requests.post(
            target_url,
            data=post_data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'CMS Commander Client/2.288'
            },
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)} 字节")
        print()
        
        # 获取标准页面用于对比
        standard_response = requests.get(target_url, timeout=10)
        
        # 比较响应
        if response.text == standard_response.text:
            print("[-] POST响应与GET响应完全相同")
            print("[-] 插件可能未处理请求")
            return
        
        print("[+] POST响应与GET响应不同！")
        print("[+] 插件正在处理请求")
        print()
        
        # 查找差异
        post_lines = response.text.split('\n')
        get_lines = standard_response.text.split('\n')
        
        print("=== 响应差异分析 ===")
        
        # 查找只在POST响应中出现的内容
        post_unique = []
        for i, line in enumerate(post_lines):
            if i >= len(get_lines) or line != get_lines[i]:
                post_unique.append((i+1, line))
        
        if post_unique:
            print("POST响应中的独特内容:")
            for line_num, line in post_unique[:20]:  # 只显示前20行差异
                if line.strip():  # 忽略空行
                    print(f"  第{line_num}行: {line.strip()}")
        
        # 查找错误消息
        print("\n=== 错误消息搜索 ===")
        error_patterns = [
            r'error[^a-zA-Z][^>]*?([^<\n]+)',
            r'invalid[^a-zA-Z][^>]*?([^<\n]+)', 
            r'signature[^a-zA-Z][^>]*?([^<\n]+)',
            r'authentication[^a-zA-Z][^>]*?([^<\n]+)',
            r'message[^a-zA-Z][^>]*?([^<\n]+)'
        ]
        
        for pattern in error_patterns:
            matches = re.findall(pattern, response.text, re.IGNORECASE)
            if matches:
                print(f"发现 '{pattern.split('[')[0]}' 相关内容:")
                for match in matches[:5]:  # 只显示前5个匹配
                    print(f"  {match.strip()}")
        
        # 查找JSON响应
        print("\n=== JSON响应搜索 ===")
        json_pattern = r'\{[^{}]*(?:"[^"]*"[^{}]*)*\}'
        json_matches = re.findall(json_pattern, response.text)
        
        for match in json_matches:
            try:
                parsed = json.loads(match)
                print(f"发现JSON: {json.dumps(parsed, indent=2)}")
            except:
                if len(match) < 200:  # 只显示短的JSON-like字符串
                    print(f"JSON-like字符串: {match}")
        
        # 查找特殊标记
        print("\n=== 特殊标记搜索 ===")
        special_markers = [
            'CMSCHEADER',
            'cmsc_response', 
            'worker_message_id',
            '##CMSC##',
            'CMS Commander'
        ]
        
        for marker in special_markers:
            if marker in response.text:
                # 找到标记周围的内容
                index = response.text.find(marker)
                start = max(0, index - 100)
                end = min(len(response.text), index + 100)
                context = response.text[start:end]
                print(f"发现标记 '{marker}':")
                print(f"  上下文: ...{context}...")
        
    except Exception as e:
        print(f"分析失败: {e}")

def test_minimal_request(target_url):
    """测试最小化请求"""
    print("\n=== 最小化请求测试 ===")
    
    # 最简单的请求
    minimal_payload = {
        "cmsc": "yes"
    }
    
    post_data = "##CMSC##" + json.dumps(minimal_payload)
    
    try:
        response = requests.post(
            target_url,
            data=post_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            timeout=10
        )
        
        print(f"最小请求状态码: {response.status_code}")
        
        # 检查是否有特殊响应
        if len(response.text) < 1000:
            print(f"短响应内容: {response.text}")
        elif "error" in response.text.lower():
            print("响应包含错误信息")
        
    except Exception as e:
        print(f"最小请求失败: {e}")

if __name__ == "__main__":
    target = "http://192.168.76.135/"
    
    print("CMS Commander 响应分析工具")
    print("=" * 50)
    
    analyze_plugin_response(target)
    test_minimal_request(target)
    
    print("\n分析完成")
