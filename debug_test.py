#!/usr/bin/env python3
"""
CMS Commander 插件调试测试脚本
用于诊断405错误和插件状态
"""

import requests
import json
from urllib.parse import urljoin

class CMSCommanderDebugger:
    def __init__(self, target_url):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def test_basic_connectivity(self):
        """测试基本连接性"""
        print("=== 基本连接性测试 ===")
        
        try:
            response = self.session.get(self.target_url, timeout=10)
            print(f"[+] 主页访问: {response.status_code}")
            
            if "WordPress" in response.text or "wp-content" in response.text:
                print("[+] 确认为WordPress站点")
                return True
            else:
                print("[-] 可能不是WordPress站点")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"[-] 连接失败: {e}")
            return False

    def check_plugin_files(self):
        """检查插件文件"""
        print("\n=== 插件文件检查 ===")
        
        plugin_files = [
            '/wp-content/plugins/cms-commander-client/',
            '/wp-content/plugins/cms-commander-client/init.php',
            '/wp-content/plugins/cms-commander-client/readme.txt',
            '/wp-content/plugins/cms-commander-client/functions.php',
            '/wp-content/plugins/cms-commander-client/version'
        ]
        
        found_files = []
        
        for file_path in plugin_files:
            url = self.target_url + file_path
            try:
                response = self.session.get(url, timeout=5)
                status = response.status_code
                
                if status == 200:
                    print(f"[+] {file_path} - 存在 (200)")
                    found_files.append(file_path)
                    
                    # 检查内容
                    if 'init.php' in file_path and 'CMS Commander' in response.text:
                        print("    [+] 确认为CMS Commander插件")
                    elif 'readme.txt' in file_path:
                        lines = response.text.split('\n')[:5]
                        for line in lines:
                            if 'CMS Commander' in line or 'Version:' in line:
                                print(f"    [+] {line.strip()}")
                                
                elif status == 403:
                    print(f"[!] {file_path} - 禁止访问 (403)")
                    found_files.append(file_path)
                elif status == 404:
                    print(f"[-] {file_path} - 不存在 (404)")
                else:
                    print(f"[?] {file_path} - 状态码: {status}")
                    
            except requests.exceptions.RequestException as e:
                print(f"[-] {file_path} - 请求失败: {e}")
        
        return len(found_files) > 0

    def test_wordpress_endpoints(self):
        """测试WordPress端点"""
        print("\n=== WordPress端点测试 ===")
        
        endpoints = [
            '/',
            '/index.php',
            '/wp-admin/',
            '/wp-admin/admin-ajax.php',
            '/wp-login.php',
            '/xmlrpc.php'
        ]
        
        for endpoint in endpoints:
            url = self.target_url + endpoint
            try:
                response = self.session.get(url, timeout=5)
                print(f"[*] {endpoint} - {response.status_code}")
                
                if endpoint == '/wp-admin/admin-ajax.php' and response.status_code == 400:
                    print("    [+] AJAX端点正常 (400是预期的)")
                    
            except requests.exceptions.RequestException as e:
                print(f"[-] {endpoint} - 失败: {e}")

    def test_post_requests(self):
        """测试POST请求"""
        print("\n=== POST请求测试 ===")
        
        # 测试数据
        test_data = "##CMSC##" + json.dumps({
            "cmsc": "yes",
            "action": "test",
            "params": {"test": "data"},
            "id": "12345",
            "signature": "test_signature",
            "url": self.target_url
        })
        
        endpoints = [
            '/',
            '/index.php',
            '/wp-admin/admin-ajax.php'
        ]
        
        for endpoint in endpoints:
            url = self.target_url + endpoint
            try:
                print(f"[*] 测试POST到: {endpoint}")
                
                response = self.session.post(
                    url,
                    data=test_data,
                    headers={
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    timeout=10
                )
                
                print(f"    状态码: {response.status_code}")
                print(f"    响应长度: {len(response.text)}")
                
                # 显示响应的前200个字符
                preview = response.text[:200].replace('\n', '\\n')
                print(f"    响应预览: {preview}")
                
                if response.status_code != 405:
                    print(f"    [+] {endpoint} 接受POST请求")
                else:
                    print(f"    [-] {endpoint} 返回405 Not Allowed")
                    
                print()
                
            except requests.exceptions.RequestException as e:
                print(f"    [-] 请求失败: {e}")
                print()

    def check_server_info(self):
        """检查服务器信息"""
        print("\n=== 服务器信息 ===")
        
        try:
            response = self.session.head(self.target_url)
            headers = response.headers
            
            server = headers.get('Server', '未知')
            print(f"[*] 服务器: {server}")
            
            if 'nginx' in server.lower():
                print("[!] 检测到Nginx服务器")
                print("    Nginx可能有特殊的配置限制POST请求")
                
            if 'X-Powered-By' in headers:
                print(f"[*] 技术栈: {headers['X-Powered-By']}")
                
        except requests.exceptions.RequestException as e:
            print(f"[-] 无法获取服务器信息: {e}")

    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("CMS Commander 插件诊断工具")
        print("=" * 50)
        print(f"目标: {self.target_url}")
        print()
        
        # 基本连接测试
        if not self.test_basic_connectivity():
            print("[!] 基本连接失败，终止诊断")
            return
        
        # 服务器信息
        self.check_server_info()
        
        # 插件文件检查
        plugin_exists = self.check_plugin_files()
        
        # WordPress端点测试
        self.test_wordpress_endpoints()
        
        # POST请求测试
        self.test_post_requests()
        
        print("=" * 50)
        print("诊断完成")
        
        if not plugin_exists:
            print("\n[!] 建议:")
            print("1. 确认CMS Commander插件已安装并激活")
            print("2. 检查插件文件权限")
            print("3. 确认WordPress配置正确")
        else:
            print("\n[!] 插件存在但可能:")
            print("1. 需要特定的认证签名")
            print("2. 服务器配置阻止了请求")
            print("3. 插件版本不匹配")

if __name__ == "__main__":
    target = "http://192.168.76.135/"
    debugger = CMSCommanderDebugger(target)
    debugger.run_full_diagnosis()
