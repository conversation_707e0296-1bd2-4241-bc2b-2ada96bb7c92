﻿=== CMS Commander - Manage Multiple Sites ===
Contributors: thoefter
Tags: manage, manage multiple sites, manager, backups, affiliate, cmscommander, Manage WordPress, Managed WordPress, WordPress management, WordPress manager, WordPress management, site management, control multiple sites, WordPress management dashboard, clone, automatic, amazon, youtube, videos, managewp, Dropbox
Tested up to: 6.6
Stable tag: 2.288

CMS Commander helps you to manage multiple WordPress sites much faster from a single powerful dashboard.

== Description ==

With [CMS Commander](http://cmscommander.com/ "Manage multiple WordPress sites") you can manage multiple WordPress sites from a single powerful dashboard. We help you control, monitor and backup all your websites - no matter if you have one or hundreds.

But CMS Commander is more than just another WordPress management solution. We do also provide you with essential marketing tools to optimize your content and better monetize your sites. With CMS Commander you will save time and earn more.

The full list of things you can do with CMS Commander is long. Have a look at our [features page](http://cmscommander.com/cms-commander-features/ "All features in CMS Commander") on cmscommander.com to get a full overview of how we can help you manage WordPress. 

The following are some of our 3,000+ users favorite features:

= Manage = 

*  All your WordPress sites in one powerful dashboard.
*  Update plugins, themes and WordPress itself on all blogs.
*  Do everything in bulk! Install plugins on multiple sites, manage users, bulk edit content and more.

= Monitor =  

*  Google Analytics integration: View aggregated traffic stats of your whole site network.
*  Uptime monitoring built in.
*  Backlink and pagespeed tracking

= Optimize = 
 
*  Over 20 APIs provide affiliate ads, images, videos and other useful free content you can use.
*  Monetize your sites better by posting relevant ads and affiliate products.
*  Improve your articles with free creative commons images and other high quality content

= Backup = 

*  Create full backups of your sites and restore then with a single click
*  Schedule automatic backup tasks and save your backups to Dropbox, Google Drive or Amazon S3
*  Copy an entire website with our powerful "Clone" tools.

Sounds good? [Sign up for your free account now](http://cmscommander.com/ "Manage your WordPress sites faster now!") to get started with CMS Commander!

A free trial account allows you to test all of our features for 14 days. See here for more details about our [premium plans](http://cmscommander.com/pricing-and-plans/ "CMS Commander pricing and plans").

The CMS Commander WordPress plugin handles the secure communication between your individual websites and your WordPress management dashboard at cmscommander.com and needs to be installed on each site you want to manage remotely with us.



Credits: [Vladimir Prelovac](http://prelovac.com/vladimir) for his worker plugin on which the CMS Commander plugin is based.

== Frequently Asked Questions ==

= Why Do I Need CMS Commander? =

If you own many WordPress sites you certainly know how cumbersome it can be to take care of them all. CMS Commander changes that: By managing all your sites in bulk from a single dashboard you can save a lot of time. Furthermore CMS Commander also helps you monetize your sites and secures them by creating automatic backups.

= Is CMS Commander Free? =

CMS Commander is a premium service but a 14 day trial account (no payment details needed) is available to allow you to test all our features. For details please refer to our [pricing page](http://cmscommander.com/pricing-and-plans/).

= Is CMS Commander secure? =

Yes, we take security serious and implemented lots of safeguards to keep your managed sites secure. It the 5+ years that CMS Commander has been operating there has been no security incident as a result of our care. You do not need to enter admin passwords to manage sites in your CMS Commander account and all communication is sent through a secure SSL-encrypted connection. You can furthermore secure your CMS Commander account with 2-factor authentication.

= Does CMS Commander leave a footprint? =

No, CMS Commander does not leave a footprint on the sites you manage with it since it operated entirely in the back end. Our service is save to use with PBNs and can not be detected.


== Changelog == 

= 2.288 =

- Fixes a security issue, which could affect sites with this plugin active that were not yet added to the CMS Commander dashboard
- Compatibility fix for php8

= 2.287 =

- Further php8 compatibility fixes

= 2.286 =

- Fixes related to compatibility with php8

= 2.285 =

- Updates Google Drive backups to account for API changes that broke upload for certain server configs.

= 2.282 = 

- Version change only, because WordPress was not recognizing an available update for previous hotfix version 2.272 to 2.28.

= 2.28 = 

- Fixes a problem that prevented automatic backup tasks from processing correctly

= 2.27 = 

- Bugfixes related to latest updates

= 2.26 =

- Several fixes for new Dropbox API V2 implementation

= 2.25 =

- Adds support for Dropbox API V2 

= 2.24 =

- Fixes custom post types not returning properly when searching a site for content.
- Improves search for specific keywords in content when searching multiple sites.
- Adds setting to CMS Commander editor to allow users to change the permalink.

= 2.23 =

- Bug fix: Repairs the search posts feature in CMS Commander, which has not been working correctly.

= 2.22 =

- Security fix
- Minor performance improvements for database cleanups

= 2.21 =

- Improvements to the backup procedure and performance
- Improves compatibility with InfiniteWP and ManageWP
- Fixes to the optimization and database cleanup feature
- Various minor bug fixes

= 2.20 =

- Better backup performance in cases where certain other plugins or backup solutions are used
- Improved syncing of new updates and comments with the dashboard
- Several minor fixes

= 2.19 =

- Fix: Bug that prevented certain premium plugin updates from being reported
- Fix: Better recognization of available updates

= 2.18 =

- Fix: Changes to the cleanup feature to make it work more reliably
- Several minor fixes

= 2.17 =

- Fix: Bug that could cause backup tasks to not get deleted correctly
- Fix: Bug that caused php warnings being thrown and saved to the log
- Fix: Plugin was not getting deactivated properly when site was deleted from CMS Commander

= 2.16 =

- Fix: Fixes a bug with the 1-click login feature that had been introduced by the previous update

= 2.15 =

- Added: Support for updates of premium plugins
- Added: Bulk edit posts feature
- Improved: Performance of the client plugin

= 2.13 =

- Added: New classes for Dropbox, S3 and Google Drive integration
- Improved: Performance of remote backup uploads for large sites
- Fixed: Bug with Google Analytics verification

= 2.12 =

- Added: Support for new [deploy WordPress](http://cmscommander.com/cms-commander-features/deploy-wordpress/) feature
- Fixed: Improved performance for backup tasks on larger sites

= 2.11 =

- Added: Backlink tracking
- Added: Pagespeed tracking
- Fixed: Incompatibility with Ninja Forms and possibly other plugins

= 2.10 =

- Fixed: Bug that prevented Backups from being uploaded to Dropbox
- Improved: Support for sites using SSL

= 2.09 =

- Fixed: Bug that prevents CMS Commander dashboard to recognize the latest version of the client was installed.

= 2.08 =

- Added: Improved post editor with presets for product/forum posts
- Fixed: Several minor bug fixes and code enhancements

= 2.07 =

- Added: Copy plugin settings feature for certain popular plugins
- Added: Easier way to readd and remove websites from an account
- Fix: Prevents deleted or paused backup tasks from running in certain situations.
- Update: New Amazon S3 class

= 2.06 =
- Added: database optimization feature
- Added: clear spam comments and misc tools feature
- Fixed: Improved automatic updating of CMS Commander client

= 2.05 =
- Added: Activity log
- Fixed: Bug that prevented correct password setup on bulk user account creation
- Fixed: Improved automatic plugin and theme updates

= 2.04 =
- Added: New comments managements features: reply to comments, bulk delete and empty trash/spam
- Added: Support for connecting multiple Google Analytics accounts
- Added: Dashboard site data is updated automatically in regular intervals

= 2.03 =
- Added: Support for managing automatic updates on all WordPress blogs
- Added: Ignore or install single plugin updates on the dashboard
- Fixed: Minor bugs related to WP 3.8

= 2.02 =
- First release in WP plugin directory.

== Installation ==

1. Upload the plugin folder to your /wp-content/plugins/ directory
2. Go to the "Plugins" page in your WordPress admin and activate the CMS Commander plugin
3. Visit [CMSCommander.com](http://cmscommander.com/ "Easy WordPress management") and sign up for free to add your site
4. Login to your CMS Commander account and add your sites.




== Screenshots ==

1. The CMS Commander dashboard - here you see all your websites, Analytics stats, uptime, available updates, new comments, post drafts and more.


== License ==

This file is part of the CMS Commander Plugin.

The CMS Commander Plugin is free software: you can redistribute it and/or modify it under the terms of the GNU General Public License as published by the Free Software Foundation, either version 3 of the License, or (at your option) any later version.

The CMS Commander Plugin is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License along with CMS Commander. If not, see <http://www.gnu.org/licenses/>.