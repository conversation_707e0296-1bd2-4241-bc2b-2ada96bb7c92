# CMS Commander 代理测试工具使用说明

## 🎯 概述

这些脚本用于通过HTTP代理测试CMS Commander WordPress插件的安全漏洞。使用代理可以让我们详细分析HTTP流量，更好地理解插件的行为和潜在的绕过技术。

## 🛠️ 工具列表

### 1. `simple_proxy_test.py` - 基础测试
**用途**: 基本的连接和功能测试
**特点**: 
- 验证代理连接
- 测试插件响应
- 基础的危险载荷测试
- 简单的签名绕过尝试

### 2. `proxy_exploit.py` - 综合测试
**用途**: 全面的漏洞利用测试
**特点**:
- 高级签名绕过技术
- 参数注入测试
- 编码绕过测试
- 不同HTTP方法测试

### 3. `signature_bypass.py` - 签名专项测试
**用途**: 专门针对签名验证的绕过测试
**特点**:
- 弱签名测试
- 签名操作测试
- 参数污染测试
- 时序攻击测试

## 🔧 环境准备

### 1. 安装依赖
```bash
pip install requests urllib3 pycryptodome
```

### 2. 设置HTTP代理
推荐使用以下代理工具之一：

**Burp Suite (推荐)**:
- 启动Burp Suite
- 设置代理监听 127.0.0.1:9000
- 配置拦截规则

**OWASP ZAP**:
- 启动ZAP
- 设置本地代理 127.0.0.1:9000

**mitmproxy**:
```bash
mitmproxy -p 9000 --listen-host 127.0.0.1
```

### 3. 目标配置
修改脚本中的目标URL：
```python
target_url = "http://**************/"  # 修改为你的目标
```

## 🚀 使用方法

### 基础测试
```bash
python simple_proxy_test.py
```

### 综合测试
```bash
python proxy_exploit.py
```

### 签名绕过测试
```bash
python signature_bypass.py
```

## 📊 测试结果分析

### 成功指标
- **短响应**: 响应长度 < 1000字节，可能是插件直接响应
- **时间戳更新**: 响应中包含 `wcStoreApiNonceTimestamp`，表示插件处理了请求
- **错误信息**: 响应包含 "error", "invalid", "signature" 等关键词
- **状态码变化**: 非200状态码可能表示特殊处理

### 失败指标
- **标准响应**: 返回完整的WordPress页面（通常 > 50KB）
- **405错误**: 方法不被允许
- **超时**: 请求超时或连接失败

## 🔍 代理分析要点

### 在Burp Suite中查看
1. **Proxy > HTTP history** - 查看所有请求
2. **Request** 标签 - 分析发送的载荷格式
3. **Response** 标签 - 分析服务器响应
4. **Length** 列 - 快速识别异常响应

### 关键观察点
- 请求格式是否正确发送
- 响应长度变化
- 响应时间差异
- 错误消息内容
- HTTP头信息变化

## 🎯 测试策略

### 1. 渐进式测试
```
基础连接 → 插件识别 → 签名绕过 → 危险载荷
```

### 2. 载荷变化
- 修改签名值
- 改变消息ID
- 调整参数结构
- 尝试不同编码

### 3. 响应分析
- 比较响应差异
- 识别错误模式
- 查找成功标识
- 监控时间变化

## ⚠️ 注意事项

### 安全提醒
- **仅用于授权测试**: 只在你拥有或有明确授权的系统上使用
- **备份数据**: 测试前备份目标系统
- **监控影响**: 注意测试对生产系统的影响

### 技术限制
- **签名验证**: 插件的签名验证可能阻止大部分攻击
- **网络环境**: 代理设置可能影响测试结果
- **目标版本**: 不同版本的插件可能有不同行为

## 🔧 故障排除

### 代理连接问题
```bash
# 检查代理是否运行
netstat -an | grep 9000

# 测试代理连接
curl -x http://127.0.0.1:9000 http://httpbin.org/ip
```

### Python依赖问题
```bash
# 安装缺失的包
pip install requests urllib3

# 对于加密功能
pip install pycryptodome
```

### 目标连接问题
```bash
# 直接测试目标
curl http://**************/

# 检查插件存在
curl http://**************/wp-content/plugins/cms-commander-client/
```

## 📈 结果解读

### 插件存在且活跃
- 时间戳持续更新
- 响应长度变化
- 包含插件相关错误信息

### 签名验证有效
- 所有请求返回相似响应
- 没有明显的错误消息
- 响应长度基本一致

### 潜在绕过成功
- 短响应或JSON响应
- 明确的错误消息
- 状态码变化

## 📚 扩展阅读

- [WordPress插件安全测试指南](https://developer.wordpress.org/plugins/security/)
- [HTTP代理工具使用](https://portswigger.net/burp/documentation)
- [Web应用安全测试方法](https://owasp.org/www-project-web-security-testing-guide/)

---

**免责声明**: 这些工具仅用于安全研究和授权的渗透测试。使用者需要确保遵守相关法律法规和道德准则。
